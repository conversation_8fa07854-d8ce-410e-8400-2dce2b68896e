import { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { registerWithEmailAndPassword, signInWithGoogle, sendVerificationEmail } from '@/services/auth';
import { createUserProfile } from '@/services/userService';
import { AuthError, FieldError, LoadingSpinner, PasswordStrength } from '@/components/ui/auth-error';
import { validateEmail, validatePassword, validateFullName, AuthErrorInfo } from '@/utils/authErrors';
import { Eye, EyeOff } from 'lucide-react';

const RegisterPage = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [fullName, setFullName] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isGoogleLoading, setIsGoogleLoading] = useState(false);
  const [authError, setAuthError] = useState<AuthErrorInfo | null>(null);
  const [fieldErrors, setFieldErrors] = useState<{
    fullName?: string;
    email?: string;
    password?: string;
  }>({});
  const navigate = useNavigate();

  // Password validation state
  const passwordValidation = validatePassword(password);

  const clearErrors = () => {
    setAuthError(null);
    setFieldErrors({});
  };

  const validateForm = (): boolean => {
    const errors: { fullName?: string; email?: string; password?: string } = {};

    // Full name validation
    const nameValidation = validateFullName(fullName);
    if (!nameValidation.isValid) {
      errors.fullName = nameValidation.error;
    }

    // Email validation
    const emailValidation = validateEmail(email);
    if (!emailValidation.isValid) {
      errors.email = emailValidation.error;
    }

    // Password validation
    if (!passwordValidation.isValid) {
      errors.password = passwordValidation.errors[0] || 'Password is invalid';
    }

    setFieldErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    clearErrors();

    if (!validateForm()) {
      return;
    }

    setIsLoading(true);

    try {
      const result = await registerWithEmailAndPassword(email, password);

      if (result.success && result.user) {
        await createUserProfile(result.user, { fullName });
        await sendVerificationEmail();
        navigate('/verify-email');
      } else if (result.error) {
        if (result.error.field && result.error.field !== 'general') {
          setFieldErrors({ [result.error.field]: result.error.message });
        } else {
          setAuthError(result.error);
        }
      }
    } catch (error) {
      setAuthError({
        code: 'unknown',
        message: 'An unexpected error occurred. Please try again.',
        field: 'general',
        severity: 'error'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoogleSignIn = async () => {
    clearErrors();
    setIsGoogleLoading(true);

    try {
      const result = await signInWithGoogle();

      if (result.success && result.user) {
        navigate('/');
      } else if (result.error) {
        setAuthError(result.error);
      }
    } catch (error) {
      setAuthError({
        code: 'unknown',
        message: 'An unexpected error occurred during Google sign-in. Please try again.',
        field: 'general',
        severity: 'error'
      });
    } finally {
      setIsGoogleLoading(false);
    }
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-gradient-to-br from-background via-secondary/20 to-background p-4">
      <div className="w-full max-w-md p-8 space-y-6 bg-white dark:bg-card rounded-xl shadow-lg border border-border card-elevated">
        <div className="text-center space-y-2">
          <h2 className="text-3xl font-bold text-foreground">Create Account</h2>
          <p className="text-muted-foreground">Join us to start your car rental journey</p>
        </div>

        {/* General Auth Error */}
        <AuthError error={authError} />

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="space-y-2">
            <Label htmlFor="fullName" className="text-sm font-medium">Full Name</Label>
            <Input
              id="fullName"
              type="text"
              value={fullName}
              onChange={(e) => {
                setFullName(e.target.value);
                if (fieldErrors.fullName) {
                  setFieldErrors(prev => ({ ...prev, fullName: undefined }));
                }
              }}
              placeholder="Enter your full name"
              className={fieldErrors.fullName ? 'border-red-500 focus:border-red-500' : ''}
              disabled={isLoading || isGoogleLoading}
            />
            <FieldError error={fieldErrors.fullName} />
          </div>

          <div className="space-y-2">
            <Label htmlFor="email" className="text-sm font-medium">Email Address</Label>
            <Input
              id="email"
              type="email"
              value={email}
              onChange={(e) => {
                setEmail(e.target.value);
                if (fieldErrors.email) {
                  setFieldErrors(prev => ({ ...prev, email: undefined }));
                }
              }}
              placeholder="Enter your email address"
              className={fieldErrors.email ? 'border-red-500 focus:border-red-500' : ''}
              disabled={isLoading || isGoogleLoading}
            />
            <FieldError error={fieldErrors.email} />
          </div>

          <div className="space-y-2">
            <Label htmlFor="password" className="text-sm font-medium">Password</Label>
            <div className="relative">
              <Input
                id="password"
                type={showPassword ? 'text' : 'password'}
                value={password}
                onChange={(e) => {
                  setPassword(e.target.value);
                  if (fieldErrors.password) {
                    setFieldErrors(prev => ({ ...prev, password: undefined }));
                  }
                }}
                placeholder="Create a strong password"
                className={fieldErrors.password ? 'border-red-500 focus:border-red-500 pr-10' : 'pr-10'}
                disabled={isLoading || isGoogleLoading}
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground transition-colors"
                disabled={isLoading || isGoogleLoading}
              >
                {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
              </button>
            </div>
            <FieldError error={fieldErrors.password} />
            <PasswordStrength password={password} validation={passwordValidation} />
          </div>

          <Button
            type="submit"
            className="w-full btn-gradient h-11 text-base font-medium"
            disabled={isLoading || isGoogleLoading || !passwordValidation.isValid}
          >
            {isLoading ? (
              <div className="flex items-center gap-2">
                <LoadingSpinner size="sm" />
                <span>Creating account...</span>
              </div>
            ) : (
              'Create Account'
            )}
          </Button>
        </form>
        <div className="relative my-6">
          <div className="absolute inset-0 flex items-center">
            <span className="w-full border-t border-border"></span>
          </div>
          <div className="relative flex justify-center text-xs uppercase">
            <span className="bg-white dark:bg-card px-3 text-muted-foreground font-medium">
              Or continue with
            </span>
          </div>
        </div>

        <Button
          variant="outline"
          className="w-full h-11 text-base font-medium border-2 hover:bg-secondary/50 transition-colors"
          onClick={handleGoogleSignIn}
          disabled={isLoading || isGoogleLoading}
        >
          {isGoogleLoading ? (
            <div className="flex items-center gap-2">
              <LoadingSpinner size="sm" />
              <span>Signing up with Google...</span>
            </div>
          ) : (
            <div className="flex items-center gap-2">
              <svg className="w-5 h-5" viewBox="0 0 24 24">
                <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
              </svg>
              <span>Sign up with Google</span>
            </div>
          )}
        </Button>

        <div className="text-center">
          <p className="text-sm text-muted-foreground">
            Already have an account?{' '}
            <Link
              to="/login"
              className="text-primary hover:text-primary/80 font-medium transition-colors"
            >
              Sign in here
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
};

export default RegisterPage;
