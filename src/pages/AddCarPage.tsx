import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ImageUpload, UploadedImage } from '@/components/ui/image-upload';
import { AuthError, LoadingSpinner } from '@/components/ui/auth-error';
import { addCar } from '@/services/carService';
import { uploadMultipleImages, ImageUploadProgress } from '@/services/imageService';
import { Car } from '@/types';
import { ArrowLeft, Plus, X } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

const AddCarPage = () => {
  const navigate = useNavigate();
  const { toast } = useToast();

  // Basic Information
  const [name, setName] = useState('');
  const [brand, setBrand] = useState('');
  const [model, setModel] = useState('');
  const [year, setYear] = useState('');
  const [description, setDescription] = useState('');
  const [category, setCategory] = useState<Car['category'] | ''>('');

  // Specifications
  const [transmission, setTransmission] = useState<Car['specifications']['transmission'] | ''>('');
  const [fuelType, setFuelType] = useState<Car['specifications']['fuelType'] | ''>('');
  const [seats, setSeats] = useState('');
  const [doors, setDoors] = useState('');
  const [engineSize, setEngineSize] = useState('');
  const [fuelConsumption, setFuelConsumption] = useState('');

  // Features
  const [features, setFeatures] = useState<string[]>([]);
  const [newFeature, setNewFeature] = useState('');

  // Pricing
  const [pricePerHour, setPricePerHour] = useState('');
  const [pricePerDay, setPricePerDay] = useState('');
  const [pricePerWeek, setPricePerWeek] = useState('');

  // Availability
  const [location, setLocation] = useState('');
  const [pickupLocations, setPickupLocations] = useState<string[]>([]);
  const [newPickupLocation, setNewPickupLocation] = useState('');

  // Images
  const [images, setImages] = useState<UploadedImage[]>([]);

  // Form state
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Helper functions
  const addFeature = () => {
    if (newFeature.trim() && !features.includes(newFeature.trim())) {
      setFeatures([...features, newFeature.trim()]);
      setNewFeature('');
    }
  };

  const removeFeature = (featureToRemove: string) => {
    setFeatures(features.filter(feature => feature !== featureToRemove));
  };

  const addPickupLocation = () => {
    if (newPickupLocation.trim() && !pickupLocations.includes(newPickupLocation.trim())) {
      setPickupLocations([...pickupLocations, newPickupLocation.trim()]);
      setNewPickupLocation('');
    }
  };

  const removePickupLocation = (locationToRemove: string) => {
    setPickupLocations(pickupLocations.filter(location => location !== locationToRemove));
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // Basic validation
    if (!name.trim()) newErrors.name = 'Car name is required';
    if (!brand.trim()) newErrors.brand = 'Brand is required';
    if (!model.trim()) newErrors.model = 'Model is required';
    if (!year.trim()) newErrors.year = 'Year is required';
    if (!description.trim()) newErrors.description = 'Description is required';
    if (!category) newErrors.category = 'Category is required';
    if (!transmission) newErrors.transmission = 'Transmission is required';
    if (!fuelType) newErrors.fuelType = 'Fuel type is required';
    if (!seats.trim()) newErrors.seats = 'Number of seats is required';
    if (!doors.trim()) newErrors.doors = 'Number of doors is required';
    if (!pricePerHour.trim()) newErrors.pricePerHour = 'Price per hour is required';
    if (!pricePerDay.trim()) newErrors.pricePerDay = 'Price per day is required';
    if (!pricePerWeek.trim()) newErrors.pricePerWeek = 'Price per week is required';
    if (!location.trim()) newErrors.location = 'Main location is required';

    // Numeric validation
    if (year && (isNaN(Number(year)) || Number(year) < 1900 || Number(year) > new Date().getFullYear() + 1)) {
      newErrors.year = 'Please enter a valid year';
    }
    if (seats && (isNaN(Number(seats)) || Number(seats) < 1 || Number(seats) > 20)) {
      newErrors.seats = 'Please enter a valid number of seats (1-20)';
    }
    if (doors && (isNaN(Number(doors)) || Number(doors) < 2 || Number(doors) > 6)) {
      newErrors.doors = 'Please enter a valid number of doors (2-6)';
    }
    if (pricePerHour && (isNaN(Number(pricePerHour)) || Number(pricePerHour) <= 0)) {
      newErrors.pricePerHour = 'Please enter a valid price per hour';
    }
    if (pricePerDay && (isNaN(Number(pricePerDay)) || Number(pricePerDay) <= 0)) {
      newErrors.pricePerDay = 'Please enter a valid price per day';
    }
    if (pricePerWeek && (isNaN(Number(pricePerWeek)) || Number(pricePerWeek) <= 0)) {
      newErrors.pricePerWeek = 'Please enter a valid price per week';
    }

    // Image validation
    if (images.length === 0) {
      newErrors.images = 'At least one image is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      toast({
        title: "Validation Error",
        description: "Please fix the errors in the form before submitting.",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);

    try {
      // Upload images first
      const imageUploadResult = await uploadMultipleImages(
        images.filter(img => img.uploadStatus !== 'error'),
        `cars/${brand.toLowerCase()}-${model.toLowerCase()}-${Date.now()}`,
        (updates: ImageUploadProgress[]) => {
          // Update image upload progress
          const updatedImages = images.map(img => {
            const update = updates.find(u => u.imageId === img.id);
            if (update) {
              return {
                ...img,
                uploadProgress: update.progress,
                uploadStatus: update.status,
                url: update.url
              };
            }
            return img;
          });
          setImages(updatedImages);
        }
      );

      if (!imageUploadResult.success) {
        throw new Error('Failed to upload images');
      }

      // Prepare car data
      const carData: Omit<Car, 'id' | 'createdAt' | 'updatedAt'> = {
        name: name.trim(),
        brand: brand.trim(),
        model: model.trim(),
        year: Number(year),
        description: description.trim(),
        images: imageUploadResult.urls,
        category: category as Car['category'],
        specifications: {
          transmission: transmission as Car['specifications']['transmission'],
          fuelType: fuelType as Car['specifications']['fuelType'],
          seats: Number(seats),
          doors: Number(doors),
          engineSize: engineSize.trim(),
          fuelConsumption: fuelConsumption.trim(),
        },
        features,
        pricing: {
          perHour: Number(pricePerHour),
          perDay: Number(pricePerDay),
          perWeek: Number(pricePerWeek),
        },
        availability: {
          isAvailable: true,
          location: location.trim(),
          pickupLocations,
        },
      };

      const result = await addCar(carData);

      if (result.success) {
        toast({
          title: "Success!",
          description: "Car has been added successfully.",
        });

        // Navigate back to admin dashboard or car list
        navigate('/admin/cars');
      } else {
        throw new Error(result.message || 'Failed to add car');
      }
    } catch (error: any) {
      console.error('Error adding car:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to add car. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-secondary/20 to-background">
      <div className="container mx-auto py-8 px-4">
        {/* Header */}
        <div className="flex items-center gap-4 mb-8">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate(-1)}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="w-4 h-4" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-foreground">Add New Car</h1>
            <p className="text-muted-foreground">Add a new vehicle to your rental fleet</p>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="max-w-4xl mx-auto space-y-8">
          {/* Basic Information */}
          <Card className="card-elevated">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                Basic Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="name">Car Name *</Label>
                  <Input
                    id="name"
                    value={name}
                    onChange={(e) => setName(e.target.value)}
                    placeholder="e.g., BMW X5 Premium"
                    className={errors.name ? 'border-red-500' : ''}
                    disabled={isSubmitting}
                  />
                  {errors.name && <p className="text-sm text-red-600">{errors.name}</p>}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="brand">Brand *</Label>
                  <Input
                    id="brand"
                    value={brand}
                    onChange={(e) => setBrand(e.target.value)}
                    placeholder="e.g., BMW"
                    className={errors.brand ? 'border-red-500' : ''}
                    disabled={isSubmitting}
                  />
                  {errors.brand && <p className="text-sm text-red-600">{errors.brand}</p>}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="model">Model *</Label>
                  <Input
                    id="model"
                    value={model}
                    onChange={(e) => setModel(e.target.value)}
                    placeholder="e.g., X5"
                    className={errors.model ? 'border-red-500' : ''}
                    disabled={isSubmitting}
                  />
                  {errors.model && <p className="text-sm text-red-600">{errors.model}</p>}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="year">Year *</Label>
                  <Input
                    id="year"
                    type="number"
                    value={year}
                    onChange={(e) => setYear(e.target.value)}
                    placeholder="e.g., 2023"
                    min="1900"
                    max={new Date().getFullYear() + 1}
                    className={errors.year ? 'border-red-500' : ''}
                    disabled={isSubmitting}
                  />
                  {errors.year && <p className="text-sm text-red-600">{errors.year}</p>}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="category">Category *</Label>
                  <Select value={category} onValueChange={setCategory} disabled={isSubmitting}>
                    <SelectTrigger className={errors.category ? 'border-red-500' : ''}>
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="economy">Economy</SelectItem>
                      <SelectItem value="luxury">Luxury</SelectItem>
                      <SelectItem value="suv">SUV</SelectItem>
                      <SelectItem value="sports">Sports</SelectItem>
                      <SelectItem value="sedan">Sedan</SelectItem>
                      <SelectItem value="convertible">Convertible</SelectItem>
                    </SelectContent>
                  </Select>
                  {errors.category && <p className="text-sm text-red-600">{errors.category}</p>}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description *</Label>
                <Textarea
                  id="description"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  placeholder="Describe the car's features, comfort, and unique selling points..."
                  rows={4}
                  className={errors.description ? 'border-red-500' : ''}
                  disabled={isSubmitting}
                />
                {errors.description && <p className="text-sm text-red-600">{errors.description}</p>}
              </div>
            </CardContent>
          </Card>

          {/* Specifications */}
          <Card className="card-elevated">
            <CardHeader>
              <CardTitle>Specifications</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="transmission">Transmission *</Label>
                  <Select value={transmission} onValueChange={setTransmission} disabled={isSubmitting}>
                    <SelectTrigger className={errors.transmission ? 'border-red-500' : ''}>
                      <SelectValue placeholder="Select transmission" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="automatic">Automatic</SelectItem>
                      <SelectItem value="manual">Manual</SelectItem>
                    </SelectContent>
                  </Select>
                  {errors.transmission && <p className="text-sm text-red-600">{errors.transmission}</p>}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="fuelType">Fuel Type *</Label>
                  <Select value={fuelType} onValueChange={setFuelType} disabled={isSubmitting}>
                    <SelectTrigger className={errors.fuelType ? 'border-red-500' : ''}>
                      <SelectValue placeholder="Select fuel type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="petrol">Petrol</SelectItem>
                      <SelectItem value="diesel">Diesel</SelectItem>
                      <SelectItem value="electric">Electric</SelectItem>
                      <SelectItem value="hybrid">Hybrid</SelectItem>
                    </SelectContent>
                  </Select>
                  {errors.fuelType && <p className="text-sm text-red-600">{errors.fuelType}</p>}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="seats">Number of Seats *</Label>
                  <Input
                    id="seats"
                    type="number"
                    value={seats}
                    onChange={(e) => setSeats(e.target.value)}
                    placeholder="e.g., 5"
                    min="1"
                    max="20"
                    className={errors.seats ? 'border-red-500' : ''}
                    disabled={isSubmitting}
                  />
                  {errors.seats && <p className="text-sm text-red-600">{errors.seats}</p>}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="doors">Number of Doors *</Label>
                  <Input
                    id="doors"
                    type="number"
                    value={doors}
                    onChange={(e) => setDoors(e.target.value)}
                    placeholder="e.g., 4"
                    min="2"
                    max="6"
                    className={errors.doors ? 'border-red-500' : ''}
                    disabled={isSubmitting}
                  />
                  {errors.doors && <p className="text-sm text-red-600">{errors.doors}</p>}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="engineSize">Engine Size</Label>
                  <Input
                    id="engineSize"
                    value={engineSize}
                    onChange={(e) => setEngineSize(e.target.value)}
                    placeholder="e.g., 2.0L Turbo"
                    disabled={isSubmitting}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="fuelConsumption">Fuel Consumption</Label>
                  <Input
                    id="fuelConsumption"
                    value={fuelConsumption}
                    onChange={(e) => setFuelConsumption(e.target.value)}
                    placeholder="e.g., 8.5L/100km"
                    disabled={isSubmitting}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Features */}
          <Card className="card-elevated">
            <CardHeader>
              <CardTitle>Features</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-2">
                <Input
                  value={newFeature}
                  onChange={(e) => setNewFeature(e.target.value)}
                  placeholder="Add a feature (e.g., GPS Navigation, Leather Seats)"
                  onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addFeature())}
                  disabled={isSubmitting}
                />
                <Button
                  type="button"
                  onClick={addFeature}
                  variant="outline"
                  size="sm"
                  disabled={isSubmitting}
                >
                  <Plus className="w-4 h-4" />
                </Button>
              </div>

              {features.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {features.map((feature, index) => (
                    <Badge
                      key={index}
                      variant="secondary"
                      className="flex items-center gap-1 px-3 py-1"
                    >
                      {feature}
                      <button
                        type="button"
                        onClick={() => removeFeature(feature)}
                        className="ml-1 hover:text-red-600"
                        disabled={isSubmitting}
                      >
                        <X className="w-3 h-3" />
                      </button>
                    </Badge>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Pricing */}
          <Card className="card-elevated">
            <CardHeader>
              <CardTitle>Pricing</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="pricePerHour">Price per Hour ($) *</Label>
                  <Input
                    id="pricePerHour"
                    type="number"
                    step="0.01"
                    value={pricePerHour}
                    onChange={(e) => setPricePerHour(e.target.value)}
                    placeholder="e.g., 15.00"
                    min="0"
                    className={errors.pricePerHour ? 'border-red-500' : ''}
                    disabled={isSubmitting}
                  />
                  {errors.pricePerHour && <p className="text-sm text-red-600">{errors.pricePerHour}</p>}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="pricePerDay">Price per Day ($) *</Label>
                  <Input
                    id="pricePerDay"
                    type="number"
                    step="0.01"
                    value={pricePerDay}
                    onChange={(e) => setPricePerDay(e.target.value)}
                    placeholder="e.g., 120.00"
                    min="0"
                    className={errors.pricePerDay ? 'border-red-500' : ''}
                    disabled={isSubmitting}
                  />
                  {errors.pricePerDay && <p className="text-sm text-red-600">{errors.pricePerDay}</p>}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="pricePerWeek">Price per Week ($) *</Label>
                  <Input
                    id="pricePerWeek"
                    type="number"
                    step="0.01"
                    value={pricePerWeek}
                    onChange={(e) => setPricePerWeek(e.target.value)}
                    placeholder="e.g., 700.00"
                    min="0"
                    className={errors.pricePerWeek ? 'border-red-500' : ''}
                    disabled={isSubmitting}
                  />
                  {errors.pricePerWeek && <p className="text-sm text-red-600">{errors.pricePerWeek}</p>}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Availability */}
          <Card className="card-elevated">
            <CardHeader>
              <CardTitle>Availability & Locations</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="location">Main Location *</Label>
                <Input
                  id="location"
                  value={location}
                  onChange={(e) => setLocation(e.target.value)}
                  placeholder="e.g., Downtown Los Angeles"
                  className={errors.location ? 'border-red-500' : ''}
                  disabled={isSubmitting}
                />
                {errors.location && <p className="text-sm text-red-600">{errors.location}</p>}
              </div>

              <div className="space-y-4">
                <Label>Pickup Locations</Label>
                <div className="flex gap-2">
                  <Input
                    value={newPickupLocation}
                    onChange={(e) => setNewPickupLocation(e.target.value)}
                    placeholder="Add pickup location (e.g., LAX Airport)"
                    onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addPickupLocation())}
                    disabled={isSubmitting}
                  />
                  <Button
                    type="button"
                    onClick={addPickupLocation}
                    variant="outline"
                    size="sm"
                    disabled={isSubmitting}
                  >
                    <Plus className="w-4 h-4" />
                  </Button>
                </div>

                {pickupLocations.length > 0 && (
                  <div className="flex flex-wrap gap-2">
                    {pickupLocations.map((location, index) => (
                      <Badge
                        key={index}
                        variant="outline"
                        className="flex items-center gap-1 px-3 py-1"
                      >
                        {location}
                        <button
                          type="button"
                          onClick={() => removePickupLocation(location)}
                          className="ml-1 hover:text-red-600"
                          disabled={isSubmitting}
                        >
                          <X className="w-3 h-3" />
                        </button>
                      </Badge>
                    ))}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Image Upload */}
          <Card className="card-elevated">
            <CardHeader>
              <CardTitle>Car Images</CardTitle>
            </CardHeader>
            <CardContent>
              <ImageUpload
                images={images}
                onImagesChange={setImages}
                maxImages={10}
                maxFileSize={5}
                disabled={isSubmitting}
              />
              {errors.images && <p className="text-sm text-red-600 mt-2">{errors.images}</p>}
            </CardContent>
          </Card>

          {/* Submit Button */}
          <div className="flex justify-end gap-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => navigate(-1)}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              className="btn-gradient min-w-[120px]"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <div className="flex items-center gap-2">
                  <LoadingSpinner size="sm" />
                  <span>Adding Car...</span>
                </div>
              ) : (
                'Add Car'
              )}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AddCarPage;
