import { Link } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';

const VerifyEmailPage = () => {
  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-100">
      <div className="w-full max-w-md p-8 space-y-6 bg-white rounded-lg shadow-md text-center">
        <h2 className="text-2xl font-bold">Verify Your Email</h2>
        <p className="text-gray-600">
          A verification email has been sent to your email address. Please check your inbox and click the link to verify your account.
        </p>
        <p className="text-gray-600">
          Once you've verified your email, you can log in to your account.
        </p>
        <Button asChild>
          <Link to="/login">Go to Login</Link>
        </Button>
      </div>
    </div>
  );
};

export default VerifyEmailPage;
