import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { getAllCars } from '@/services/carService';
import { Car } from '@/types';
import { Plus, Edit, Trash2, Eye, Car as CarIcon } from 'lucide-react';
import { LoadingSpinner } from '@/components/ui/auth-error';

const AdminCarsPage = () => {
  const [cars, setCars] = useState<Car[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadCars();
  }, []);

  const loadCars = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const result = await getAllCars();
      
      if (result.success && result.cars) {
        setCars(result.cars);
      } else {
        setError('Failed to load cars');
      }
    } catch (error) {
      setError('An error occurred while loading cars');
    } finally {
      setIsLoading(false);
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price);
  };

  const getCategoryColor = (category: Car['category']) => {
    const colors = {
      economy: 'bg-green-100 text-green-800',
      luxury: 'bg-purple-100 text-purple-800',
      suv: 'bg-blue-100 text-blue-800',
      sports: 'bg-red-100 text-red-800',
      sedan: 'bg-gray-100 text-gray-800',
      convertible: 'bg-yellow-100 text-yellow-800'
    };
    return colors[category] || 'bg-gray-100 text-gray-800';
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background via-secondary/20 to-background">
        <div className="container mx-auto py-8 px-4">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center space-y-4">
              <LoadingSpinner size="lg" />
              <p className="text-muted-foreground">Loading cars...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-secondary/20 to-background">
      <div className="container mx-auto py-8 px-4">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-foreground">Car Management</h1>
            <p className="text-muted-foreground">Manage your rental fleet</p>
          </div>
          <Link to="/admin/add-car">
            <Button className="btn-gradient">
              <Plus className="w-4 h-4 mr-2" />
              Add New Car
            </Button>
          </Link>
        </div>

        {/* Error State */}
        {error && (
          <Card className="mb-6 border-red-200 bg-red-50">
            <CardContent className="pt-6">
              <p className="text-red-600">{error}</p>
              <Button 
                variant="outline" 
                size="sm" 
                onClick={loadCars}
                className="mt-2"
              >
                Try Again
              </Button>
            </CardContent>
          </Card>
        )}

        {/* Cars Grid */}
        {cars.length === 0 ? (
          <Card className="text-center py-12">
            <CardContent>
              <CarIcon className="w-16 h-16 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium text-foreground mb-2">No cars found</h3>
              <p className="text-muted-foreground mb-4">
                Start building your fleet by adding your first car.
              </p>
              <Link to="/admin/add-car">
                <Button className="btn-gradient">
                  <Plus className="w-4 h-4 mr-2" />
                  Add Your First Car
                </Button>
              </Link>
            </CardContent>
          </Card>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {cars.map((car) => (
              <Card key={car.id} className="card-elevated overflow-hidden">
                {/* Car Image */}
                <div className="relative h-48 bg-muted">
                  {car.images && car.images.length > 0 ? (
                    <img
                      src={car.images[0]}
                      alt={car.name}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center">
                      <CarIcon className="w-16 h-16 text-muted-foreground" />
                    </div>
                  )}
                  
                  {/* Availability Badge */}
                  <div className="absolute top-3 right-3">
                    <Badge 
                      className={
                        car.availability?.isAvailable 
                          ? 'bg-green-500 hover:bg-green-600' 
                          : 'bg-red-500 hover:bg-red-600'
                      }
                    >
                      {car.availability?.isAvailable ? 'Available' : 'Unavailable'}
                    </Badge>
                  </div>
                </div>

                {/* Car Details */}
                <CardContent className="p-6">
                  <div className="space-y-4">
                    {/* Header */}
                    <div>
                      <div className="flex items-center justify-between mb-2">
                        <h3 className="text-lg font-semibold text-foreground truncate">
                          {car.name}
                        </h3>
                        <Badge className={getCategoryColor(car.category)}>
                          {car.category}
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        {car.brand} {car.model} • {car.year}
                      </p>
                    </div>

                    {/* Specifications */}
                    <div className="flex items-center gap-4 text-sm text-muted-foreground">
                      <span>{car.specifications?.seats} seats</span>
                      <span>•</span>
                      <span>{car.specifications?.transmission}</span>
                      <span>•</span>
                      <span>{car.specifications?.fuelType}</span>
                    </div>

                    {/* Pricing */}
                    <div className="space-y-1">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-muted-foreground">Per Day:</span>
                        <span className="font-semibold text-foreground">
                          {formatPrice(car.pricing.perDay)}
                        </span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-muted-foreground">Per Week:</span>
                        <span className="font-semibold text-foreground">
                          {formatPrice(car.pricing.perWeek)}
                        </span>
                      </div>
                    </div>

                    {/* Location */}
                    <div className="text-sm text-muted-foreground">
                      📍 {car.availability?.location}
                    </div>

                    {/* Actions */}
                    <div className="flex gap-2 pt-2">
                      <Button variant="outline" size="sm" className="flex-1">
                        <Eye className="w-4 h-4 mr-1" />
                        View
                      </Button>
                      <Button variant="outline" size="sm" className="flex-1">
                        <Edit className="w-4 h-4 mr-1" />
                        Edit
                      </Button>
                      <Button variant="outline" size="sm" className="text-red-600 hover:text-red-700">
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default AdminCarsPage;
