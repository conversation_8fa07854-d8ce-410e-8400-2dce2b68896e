import { useState } from "react";
import { useParams } from "react-router-dom";
import { Star, Settings, Fuel, Users, Navigation as NavigationIcon, Shield, CheckCircle, XCircle, ArrowLeft } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import heroSuv from "@/assets/hero-suv.jpg";
import Navigation from "@/components/Navigation";
import Footer from "@/components/Footer";
import { NavLink } from "react-router-dom";

const CarDetail = () => {
  const { id } = useParams();
  const [selectedImage, setSelectedImage] = useState(0);

  // Mock car data - in real app this would come from API
  const car = {
    id: 1,
    name: "BMW",
    type: "LUXURY SUV",
    rating: 4.8,
    reviews: 73,
    price: 25,
    images: [heroSuv, heroSuv, heroSuv, heroSuv],
    available: true,
    description: "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.",
    specifications: {
      gearbox: "Automatic",
      fuel: "Petrol", 
      seats: "5",
      doors: "5",
      airConditioner: "Yes",
      distance: "500"
    },
    technicalDetails: {
      abs: true,
      airBags: true,
      cruiseControl: true,
      airConditioner: false
    }
  };

  const recommendedCars = [
    {
      id: 2,
      name: "Mercedes",
      type: "SEDAN",
      rating: 4.8,
      reviews: 73,
      price: 25,
      image: heroSuv,
      features: ["Automatic", "Petrol", "Air Conditioner", "GPS"]
    },
    {
      id: 3,
      name: "Mercedes", 
      type: "SEDAN",
      rating: 4.8,
      reviews: 73,
      price: 25,
      image: heroSuv,
      features: ["Automatic", "Petrol", "Air Conditioner", "GPS"]
    },
    {
      id: 4,
      name: "Mercedes",
      type: "SEDAN", 
      rating: 4.8,
      reviews: 73,
      price: 25,
      image: heroSuv,
      features: ["Automatic", "Petrol", "Air Conditioner", "GPS"]
    }
  ];

  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Breadcrumb */}
        <div className="flex items-center space-x-2 text-sm mb-8">
          <NavLink to="/" className="text-muted-foreground hover:text-primary">HOME</NavLink>
          <span className="text-muted-foreground">/</span>
          <NavLink to="/cars" className="text-muted-foreground hover:text-primary">Rent a car</NavLink>
          <span className="text-muted-foreground">/</span>
          <span className="text-foreground">Car Details</span>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16">
          {/* Car Images */}
          <div>
            <div className="mb-4">
              <img 
                src={car.images[selectedImage]} 
                alt={car.name}
                className="w-full h-96 object-cover rounded-lg"
              />
            </div>
            <div className="flex space-x-4">
              {car.images.map((image, index) => (
                <button
                  key={index}
                  onClick={() => setSelectedImage(index)}
                  className={`w-20 h-20 rounded-lg overflow-hidden border-2 transition-colors ${
                    selectedImage === index ? 'border-primary' : 'border-transparent'
                  }`}
                >
                  <img 
                    src={image} 
                    alt={`${car.name} view ${index + 1}`}
                    className="w-full h-full object-cover"
                  />
                </button>
              ))}
            </div>
          </div>

          {/* Car Details */}
          <div>
            <div className="flex justify-between items-start mb-4">
              <div>
                <h1 className="text-3xl font-bold text-foreground mb-2">{car.name}</h1>
                <p className="text-muted-foreground">{car.type}</p>
              </div>
              {car.available && (
                <Badge className="bg-success">Available</Badge>
              )}
            </div>

            <div className="flex items-center star-rating mb-6">
              <Star className="w-5 h-5 fill-current" />
              <span className="text-lg font-medium ml-2">{car.rating}</span>
              <span className="text-muted-foreground ml-2">({car.reviews} Reviews)</span>
            </div>

            <div className="flex items-baseline mb-8">
              <span className="text-4xl font-bold text-foreground">${car.price}</span>
              <span className="text-lg text-muted-foreground ml-2">/day</span>
            </div>

            <Button size="lg" variant="gradient" className="w-full">
              Rent This Car
            </Button>

            <div className="space-y-4">
              <p className="text-muted-foreground leading-relaxed">
                {car.description}
              </p>
            </div>
          </div>
        </div>

        {/* Technical Specifications */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16">
          {/* Specifications */}
          <Card className="card-elevated">
            <CardContent className="p-8">
              <h2 className="text-2xl font-bold text-foreground mb-8">Technical Specification</h2>
              
              <div className="grid grid-cols-2 gap-8">
                <div className="text-center">
                  <Settings className="w-8 h-8 text-muted-foreground mx-auto mb-3" />
                  <h3 className="font-semibold text-foreground mb-1">Gear Box</h3>
                  <p className="text-muted-foreground">{car.specifications.gearbox}</p>
                </div>
                
                <div className="text-center">
                  <Fuel className="w-8 h-8 text-muted-foreground mx-auto mb-3" />
                  <h3 className="font-semibold text-foreground mb-1">Fuel</h3>
                  <p className="text-muted-foreground">{car.specifications.fuel}</p>
                </div>
                
                <div className="text-center">
                  <Users className="w-8 h-8 text-muted-foreground mx-auto mb-3" />
                  <h3 className="font-semibold text-foreground mb-1">Seats</h3>
                  <p className="text-muted-foreground">{car.specifications.seats}</p>
                </div>
                
                <div className="text-center">
                  <NavigationIcon className="w-8 h-8 text-muted-foreground mx-auto mb-3" />
                  <h3 className="font-semibold text-foreground mb-1">Distance</h3>
                  <p className="text-muted-foreground">{car.specifications.distance}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Technical Details */}
          <Card className="card-elevated">
            <CardContent className="p-8">
              <h2 className="text-2xl font-bold text-foreground mb-8">Technical Details</h2>
              
              <div className="grid grid-cols-2 gap-6">
                <div className="flex items-center">
                  {car.technicalDetails.abs ? (
                    <CheckCircle className="w-5 h-5 text-success mr-3" />
                  ) : (
                    <XCircle className="w-5 h-5 text-destructive mr-3" />
                  )}
                  <span className="text-foreground">ABS</span>
                </div>
                
                <div className="flex items-center">
                  {car.technicalDetails.airBags ? (
                    <CheckCircle className="w-5 h-5 text-success mr-3" />
                  ) : (
                    <XCircle className="w-5 h-5 text-destructive mr-3" />
                  )}
                  <span className="text-foreground">Air Bags</span>
                </div>
                
                <div className="flex items-center">
                  {car.technicalDetails.cruiseControl ? (
                    <CheckCircle className="w-5 h-5 text-success mr-3" />
                  ) : (
                    <XCircle className="w-5 h-5 text-destructive mr-3" />
                  )}
                  <span className="text-foreground">Cruise Control</span>
                </div>
                
                <div className="flex items-center">
                  {car.technicalDetails.airConditioner ? (
                    <CheckCircle className="w-5 h-5 text-success mr-3" />
                  ) : (
                    <XCircle className="w-5 h-5 text-destructive mr-3" />
                  )}
                  <span className="text-foreground">Air Conditioner</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Recommended Cars */}
        <div>
          <div className="flex justify-between items-center mb-8">
            <h2 className="text-3xl font-bold text-foreground">Recommended cars</h2>
            <Button variant="ghost-gold">
              View All →
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {recommendedCars.map((recommendedCar) => (
              <Card key={recommendedCar.id} className="card-elevated overflow-hidden">
                <div className="relative">
                  <img 
                    src={recommendedCar.image} 
                    alt={recommendedCar.name}
                    className="w-full h-48 object-cover"
                  />
                </div>
                
                <CardContent className="p-6">
                  <div className="flex justify-between items-start mb-4">
                    <div>
                      <h3 className="text-xl font-semibold text-foreground mb-1">
                        {recommendedCar.name}
                      </h3>
                      <p className="text-sm text-muted-foreground">
                        {recommendedCar.type}
                      </p>
                    </div>
                    <div className="flex items-center star-rating">
                      <Star className="w-4 h-4 fill-current" />
                      <span className="text-sm font-medium ml-1">
                        {recommendedCar.rating}
                      </span>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4 mb-6">
                    <div className="flex items-center text-sm text-muted-foreground">
                      <Settings className="spec-icon mr-2" />
                      Automatic
                    </div>
                    <div className="flex items-center text-sm text-muted-foreground">
                      <Fuel className="spec-icon mr-2" />
                      Petrol
                    </div>
                    <div className="flex items-center text-sm text-muted-foreground">
                      <Users className="spec-icon mr-2" />
                      5 Seats
                    </div>
                    <div className="flex items-center text-sm text-muted-foreground">
                      <NavigationIcon className="spec-icon mr-2" />
                      GPS
                    </div>
                  </div>

                  <div className="flex justify-between items-center">
                    <div>
                      <span className="text-2xl font-bold text-foreground">
                        ${recommendedCar.price}
                      </span>
                      <span className="text-sm text-muted-foreground">/day</span>
                    </div>
                    <Button variant="gradient">
                      Rent Now
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default CarDetail;