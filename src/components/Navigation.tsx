import { useState, useEffect } from "react";
import { NavLink, useNavigate } from "react-router-dom";
import { Menu, X, Car } from "lucide-react";
import { Button } from "@/components/ui/button";
import { signInWithGoogle, signOutUser } from "@/services/auth";
import { getAuth, onAuthStateChanged, User } from "firebase/auth";

const Navigation = () => {
  const [isOpen, setIsOpen] = useState(false);
  const navigate = useNavigate();
  const [user, setUser] = useState<User | null>(null);

  useEffect(() => {
    const auth = getAuth();
    const unsubscribe = onAuthStateChanged(auth, (currentUser) => {
      setUser(currentUser);
    });
    return () => unsubscribe();
  }, []);

  const navItems = [
    { name: "Home", path: "/" },
    { name: "Rent a Car", path: "/cars" },
    { name: "About Us", path: "/about" },
    { name: "Contact", path: "/contact" }
  ];

  const handleSignIn = async () => {
    try {
      await signInWithGoogle();
      setIsOpen(false);
    } catch (error) {
      console.error("Sign-in failed:", error);
      alert("Failed to sign in. Please try again.");
    }
  };

  const handleSignOut = async () => {
    await signOutUser();
    setIsOpen(false);
  };

  return (
    <nav className="bg-white shadow-sm sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <NavLink to="/" className="flex items-center space-x-2">
            <Car className="w-8 h-8 text-primary" />
            <span className="text-xl font-bold text-foreground">Luxury Car Rental</span>
          </NavLink>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            {navItems.map((item) => (
              <NavLink
                key={item.name}
                to={item.path}
                className={({ isActive }) =>
                  `text-sm font-medium transition-colors hover:text-primary ${
                    isActive ? "text-primary" : "text-muted-foreground"
                  }`
                }
              >
                {item.name}
              </NavLink>
            ))}
            {user ? (
              <>
                <span className="text-sm font-medium text-foreground">Welcome, {user.displayName || user.email}</span>
                <Button variant="ghost" onClick={handleSignOut}>
                  Sign Out
                </Button>
              </>
            ) : (
              <Button variant="ghost" onClick={() => navigate("/login")}>
                Sign In
              </Button>
            )}
            <Button variant="gradient">
              Book Now
            </Button>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsOpen(!isOpen)}
              className="p-2"
            >
              {isOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
            </Button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isOpen && (
          <div className="md:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 bg-white shadow-lg">
              {navItems.map((item) => (
                <NavLink
                  key={item.name}
                  to={item.path}
                  className={({ isActive }) =>
                    `block px-3 py-2 text-base font-medium transition-colors hover:text-primary ${
                      isActive ? "text-primary bg-primary/5" : "text-muted-foreground"
                    }`
                  }
                  onClick={() => setIsOpen(false)}
                >
                  {item.name}
                </NavLink>
              ))}
              <div className="px-3 pt-2">
                {user ? (
                  <span className="block w-full text-center px-3 py-2 text-base font-medium text-foreground">Welcome, {user.displayName}</span>
                
                ) : (
                <>
                  <Button variant="ghost" className="w-full" onClick={handleSignIn}>
                    Sign In
                  </Button>
                  <Button variant="ghost" className="w-full mt-2" onClick={() => { setIsOpen(false); navigate("/register"); }}>
                    Register
                  </Button>
                 
                </>
                )}
                <Button variant="gradient" className="w-full mt-4 py-3 text-center">
                  Book Now
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </nav>
  );
};

export default Navigation;