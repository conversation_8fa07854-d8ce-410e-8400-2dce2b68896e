import { Star, Users, Fuel, Settings, Navigation } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { NavLink } from "react-router-dom";
import heroSuv from "@/assets/hero-suv.jpg";

const FeaturedCars = () => {
  const cars = [
    {
      id: 1,
      name: "Mercedes",
      type: "SEDAN",
      rating: 4.8,
      reviews: 73,
      price: 25,
      image: heroSuv,
      features: ["Automatic", "Petrol", "Air Conditioner", "GPS"],
      available: true
    },
    {
      id: 2,
      name: "Mercedes",
      type: "SEDAN", 
      rating: 4.8,
      reviews: 73,
      price: 25,
      image: heroSuv,
      features: ["Automatic", "Petrol", "Air Conditioner", "GPS"],
      available: true
    },
    {
      id: 3,
      name: "Mercedes",
      type: "SEDAN",
      rating: 4.8,
      reviews: 73,
      price: 25,
      image: heroSuv,
      features: ["Automatic", "Petrol", "Air Conditioner", "GPS"],
      available: true
    }
  ];

  return (
    <section className="py-20 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        <div className="flex justify-between items-center mb-12">
          <div>
            <h2 className="text-3xl font-bold text-foreground mb-4">
              Choose The Car That Suits You
            </h2>
            <p className="text-muted-foreground">
              Select from our premium collection of luxury vehicles
            </p>
          </div>
          <Button variant="ghost-gold">
            View All →
          </Button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {cars.map((car, index) => (
            <Card 
              key={car.id} 
              className="card-elevated overflow-hidden animate-scale-in"
              style={{animationDelay: `${index * 0.1}s`}}
            >
              <div className="relative">
                <img 
                  src={car.image} 
                  alt={car.name}
                  className="w-full h-48 object-cover"
                />
                {car.available && (
                  <Badge className="absolute top-4 right-4 bg-success">
                    Available
                  </Badge>
                )}
              </div>
              
              <CardContent className="p-6">
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <h3 className="text-xl font-semibold text-foreground mb-1">
                      {car.name}
                    </h3>
                    <p className="text-sm text-muted-foreground">
                      {car.type}
                    </p>
                  </div>
                  <div className="flex items-center star-rating">
                    <Star className="w-4 h-4 fill-current" />
                    <span className="text-sm font-medium ml-1">
                      {car.rating}
                    </span>
                    <span className="text-xs text-muted-foreground ml-1">
                      ({car.reviews})
                    </span>
                  </div>
                </div>

                {/* Car Features */}
                <div className="grid grid-cols-2 gap-4 mb-6">
                  <div className="flex items-center text-sm text-muted-foreground">
                    <Settings className="spec-icon mr-2" />
                    Automatic
                  </div>
                  <div className="flex items-center text-sm text-muted-foreground">
                    <Fuel className="spec-icon mr-2" />
                    Petrol
                  </div>
                  <div className="flex items-center text-sm text-muted-foreground">
                    <Users className="spec-icon mr-2" />
                    5 Seats
                  </div>
                  <div className="flex items-center text-sm text-muted-foreground">
                    <Navigation className="spec-icon mr-2" />
                    GPS
                  </div>
                </div>

                <div className="flex justify-between items-center">
                  <div>
                    <span className="text-2xl font-bold text-foreground">
                      ${car.price}
                    </span>
                    <span className="text-sm text-muted-foreground">/day</span>
                  </div>
                  <Button variant="gradient" asChild>
                    <NavLink to={`/car/${car.id}`}>
                      Rent Now
                    </NavLink>
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};

export default FeaturedCars;