import { Car } from "lucide-react";

const CarBrands = () => {
  const brands = [
    { name: "BMW", logo: "🚗" },
    { name: "Lexus", logo: "🚗" },
    { name: "Mercedes", logo: "🚗" },
    { name: "Honda", logo: "🚗" },
    { name: "Hyundai", logo: "🚗" },
    { name: "Nissan", logo: "🚗" },
    { name: "Toyota", logo: "🚗" },
    { name: "Audi", logo: "🚗" }
  ];

  return (
    <section className="py-16 px-4 sm:px-6 lg:px-8 bg-muted/30">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-12">
          <h2 className="text-2xl font-bold text-foreground mb-4">
            Trusted by Leading Car Brands
          </h2>
          <p className="text-muted-foreground">
            Choose from our extensive collection of premium vehicles
          </p>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-8 items-center">
          {brands.map((brand, index) => (
            <div 
              key={brand.name}
              className="flex flex-col items-center justify-center p-4 hover:scale-105 transition-transform duration-300"
              style={{animationDelay: `${index * 0.1}s`}}
            >
              <Car className="w-8 h-8 text-muted-foreground mb-2" />
              <span className="text-sm font-medium text-muted-foreground">
                {brand.name}
              </span>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default CarBrands;