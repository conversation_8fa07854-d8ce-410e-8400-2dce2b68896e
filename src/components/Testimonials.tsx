import { Star, Quote } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";

const Testimonials = () => {
  const testimonials = [
    {
      id: 1,
      name: "Business Holiday",
      role: "CEO, Company",
      content: "We offer the best Experience with our rental deals. We have been in this business for more than 10 years and we have satisfied customers all over the world.",
      rating: 5,
      avatar: "BH"
    },
    {
      id: 2,
      name: "Huge Collections of Cars",
      role: "Manager, AutoCorp",
      content: "Amazing variety of luxury cars at competitive prices. The booking process was seamless and the customer service was exceptional throughout our rental period.",
      rating: 5,
      avatar: "HC"
    },
    {
      id: 3,
      name: "Mindblowing Service",
      role: "Travel Blogger",
      content: "Professional service with attention to detail. The car was in perfect condition and the staff went above and beyond to ensure our satisfaction. Highly recommended!",
      rating: 5,
      avatar: "MS"
    }
  ];

  return (
    <section className="py-20 px-4 sm:px-6 lg:px-8 bg-muted/30">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-16">
          <p className="text-sm font-medium text-primary mb-2">TESTIMONIALS</p>
          <h2 className="text-3xl font-bold text-foreground mb-4">
            Our Clients Speak
          </h2>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            See what our satisfied customers have to say about their experience with our premium car rental service.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <Card 
              key={testimonial.id}
              className="card-elevated p-6 animate-scale-in"
              style={{animationDelay: `${index * 0.1}s`}}
            >
              <CardContent className="p-0">
                {/* Quote Icon */}
                <div className="mb-4">
                  <Quote className="w-8 h-8 text-primary/30" />
                </div>

                {/* Rating */}
                <div className="flex items-center star-rating mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="w-4 h-4 fill-current" />
                  ))}
                </div>

                {/* Content */}
                <p className="text-muted-foreground mb-6 leading-relaxed">
                  {testimonial.content}
                </p>

                {/* Author */}
                <div className="flex items-center">
                  <Avatar className="w-12 h-12 mr-4">
                    <AvatarFallback className="bg-primary/10 text-primary font-semibold">
                      {testimonial.avatar}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <h4 className="font-semibold text-foreground">
                      {testimonial.name}
                    </h4>
                    <p className="text-sm text-muted-foreground">
                      {testimonial.role}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Testimonials;