import React from 'react';
import { AlertCircle, AlertTriangle, Info, CheckCircle } from 'lucide-react';
import { cn } from '@/lib/utils';
import { AuthErrorInfo } from '@/utils/authErrors';

interface AuthErrorProps {
  error: AuthErrorInfo | null;
  className?: string;
}

export const AuthError: React.FC<AuthErrorProps> = ({ error, className }) => {
  if (!error) return null;

  const getIcon = () => {
    switch (error.severity) {
      case 'error':
        return <AlertCircle className="w-4 h-4" />;
      case 'warning':
        return <AlertTriangle className="w-4 h-4" />;
      case 'info':
        return <Info className="w-4 h-4" />;
      default:
        return <AlertCircle className="w-4 h-4" />;
    }
  };

  const getStyles = () => {
    switch (error.severity) {
      case 'error':
        return 'bg-red-50 border-red-200 text-red-800 dark:bg-red-900/20 dark:border-red-800/30 dark:text-red-300';
      case 'warning':
        return 'bg-amber-50 border-amber-200 text-amber-800 dark:bg-amber-900/20 dark:border-amber-800/30 dark:text-amber-300';
      case 'info':
        return 'bg-blue-50 border-blue-200 text-blue-800 dark:bg-blue-900/20 dark:border-blue-800/30 dark:text-blue-300';
      default:
        return 'bg-red-50 border-red-200 text-red-800 dark:bg-red-900/20 dark:border-red-800/30 dark:text-red-300';
    }
  };

  return (
    <div
      className={cn(
        'flex items-start gap-2 p-3 rounded-lg border text-sm font-medium animate-slide-up',
        getStyles(),
        className
      )}
      role="alert"
      aria-live="polite"
    >
      <div className="flex-shrink-0 mt-0.5">
        {getIcon()}
      </div>
      <div className="flex-1">
        {error.message}
      </div>
    </div>
  );
};

interface FieldErrorProps {
  error?: string;
  className?: string;
}

export const FieldError: React.FC<FieldErrorProps> = ({ error, className }) => {
  if (!error) return null;

  return (
    <div
      className={cn(
        'flex items-center gap-1 mt-1 text-sm text-red-600 dark:text-red-400 animate-slide-up',
        className
      )}
      role="alert"
      aria-live="polite"
    >
      <AlertCircle className="w-3 h-3 flex-shrink-0" />
      <span>{error}</span>
    </div>
  );
};

interface PasswordStrengthProps {
  password: string;
  validation: {
    isValid: boolean;
    errors: string[];
    strength: 'weak' | 'fair' | 'good' | 'strong';
  };
  className?: string;
}

export const PasswordStrength: React.FC<PasswordStrengthProps> = ({ 
  password, 
  validation, 
  className 
}) => {
  if (!password) return null;

  const getStrengthColor = () => {
    switch (validation.strength) {
      case 'weak':
        return 'bg-red-500';
      case 'fair':
        return 'bg-amber-500';
      case 'good':
        return 'bg-blue-500';
      case 'strong':
        return 'bg-green-500';
      default:
        return 'bg-gray-300';
    }
  };

  const getStrengthWidth = () => {
    switch (validation.strength) {
      case 'weak':
        return 'w-1/4';
      case 'fair':
        return 'w-2/4';
      case 'good':
        return 'w-3/4';
      case 'strong':
        return 'w-full';
      default:
        return 'w-0';
    }
  };

  const getStrengthText = () => {
    switch (validation.strength) {
      case 'weak':
        return 'Weak';
      case 'fair':
        return 'Fair';
      case 'good':
        return 'Good';
      case 'strong':
        return 'Strong';
      default:
        return '';
    }
  };

  return (
    <div className={cn('mt-2 space-y-2', className)}>
      {/* Strength indicator */}
      <div className="space-y-1">
        <div className="flex justify-between items-center">
          <span className="text-xs text-muted-foreground">Password strength</span>
          <span className={cn(
            'text-xs font-medium',
            validation.strength === 'weak' && 'text-red-600 dark:text-red-400',
            validation.strength === 'fair' && 'text-amber-600 dark:text-amber-400',
            validation.strength === 'good' && 'text-blue-600 dark:text-blue-400',
            validation.strength === 'strong' && 'text-green-600 dark:text-green-400'
          )}>
            {getStrengthText()}
          </span>
        </div>
        <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5">
          <div
            className={cn(
              'h-1.5 rounded-full transition-all duration-300',
              getStrengthColor(),
              getStrengthWidth()
            )}
          />
        </div>
      </div>

      {/* Error messages */}
      {validation.errors.length > 0 && (
        <div className="space-y-1">
          {validation.errors.map((error, index) => (
            <div
              key={index}
              className="flex items-center gap-1 text-xs text-red-600 dark:text-red-400"
            >
              <AlertCircle className="w-3 h-3 flex-shrink-0" />
              <span>{error}</span>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({ 
  size = 'md', 
  className 
}) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6'
  };

  return (
    <div
      className={cn(
        'animate-spin rounded-full border-2 border-current border-t-transparent',
        sizeClasses[size],
        className
      )}
      role="status"
      aria-label="Loading"
    >
      <span className="sr-only">Loading...</span>
    </div>
  );
};

interface SuccessMessageProps {
  message: string;
  className?: string;
}

export const SuccessMessage: React.FC<SuccessMessageProps> = ({ 
  message, 
  className 
}) => {
  return (
    <div
      className={cn(
        'flex items-center gap-2 p-3 rounded-lg border bg-green-50 border-green-200 text-green-800 dark:bg-green-900/20 dark:border-green-800/30 dark:text-green-300 text-sm font-medium animate-slide-up',
        className
      )}
      role="alert"
      aria-live="polite"
    >
      <CheckCircle className="w-4 h-4 flex-shrink-0" />
      <span>{message}</span>
    </div>
  );
};
