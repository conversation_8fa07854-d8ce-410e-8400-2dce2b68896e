import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { MapPin, Calendar, Car } from "lucide-react";
import heroSuv from "@/assets/hero-suv.jpg";

const Hero = () => {
  return (
    <section className="hero-bg py-20 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        <div className="grid grid-cols-1 lg:grid-cols-2 items-center gap-12">
          {/* Left Content */}
          <div className="animate-slide-up">
            <h1 className="text-4xl md:text-6xl font-bold text-foreground mb-6 leading-tight">
              Discover, Reserve,<br />
              and <span className="text-gradient">Rent a Car</span> in<br />
              Simple Steps
            </h1>
            <p className="text-lg text-muted-foreground mb-8 max-w-md">
              Experience luxury and convenience with our premium car rental service. 
              Choose from a wide selection of high-quality vehicles for any occasion.
            </p>
            <Button size="lg" variant="gradient" className="text-lg px-8 py-6">
              Start Booking
            </Button>
          </div>

          {/* Right Content - Car Image */}
          <div className="animate-fade-in">
            <img 
              src={heroSuv} 
              alt="Luxury Black SUV" 
              className="w-full h-auto"
            />
          </div>
        </div>

        {/* Booking Steps */}
        <div className="mt-20">
          <div className="text-center mb-12">
            <p className="text-sm font-medium text-primary mb-2">HOW IT WORKS</p>
            <h2 className="text-3xl font-bold text-foreground">
              Rent with following 3 working steps
            </h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Step 1 */}
            <Card className="card-elevated p-8 text-center animate-scale-in">
              <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-6">
                <MapPin className="w-8 h-8 text-primary" />
              </div>
              <h3 className="text-xl font-semibold text-foreground mb-4">Choose Location</h3>
              <p className="text-muted-foreground">
                Select your preferred pickup location from our network of convenient locations.
              </p>
            </Card>

            {/* Step 2 */}
            <Card className="card-elevated p-8 text-center animate-scale-in" style={{animationDelay: '0.1s'}}>
              <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-6">
                <Calendar className="w-8 h-8 text-primary" />
              </div>
              <h3 className="text-xl font-semibold text-foreground mb-4">Pick-up Date</h3>
              <p className="text-muted-foreground">
                Choose your rental dates and times that work best for your schedule.
              </p>
            </Card>

            {/* Step 3 */}
            <Card className="card-elevated p-8 text-center animate-scale-in" style={{animationDelay: '0.2s'}}>
              <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-6">
                <Car className="w-8 h-8 text-primary" />
              </div>
              <h3 className="text-xl font-semibold text-foreground mb-4">Book Your Car</h3>
              <p className="text-muted-foreground">
                Complete your reservation and get ready to drive your perfect rental car.
              </p>
            </Card>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;