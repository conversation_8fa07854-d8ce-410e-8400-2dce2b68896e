import { AuthError } from 'firebase/auth';

export interface AuthErrorInfo {
  code: string;
  message: string;
  field?: 'email' | 'password' | 'general';
  severity: 'error' | 'warning' | 'info';
}

/**
 * Maps Firebase Auth error codes to user-friendly messages
 */
export const getAuthErrorMessage = (error: AuthError | Error): AuthErrorInfo => {
  const errorCode = 'code' in error ? error.code : 'unknown';
  
  switch (errorCode) {
    // Login errors
    case 'auth/user-not-found':
      return {
        code: errorCode,
        message: 'No account found with this email address. Please check your email or create a new account.',
        field: 'email',
        severity: 'error'
      };
    
    case 'auth/wrong-password':
    case 'auth/invalid-credential':
      return {
        code: errorCode,
        message: 'Incorrect password. Please try again or use "Forgot Password" to reset.',
        field: 'password',
        severity: 'error'
      };
    
    case 'auth/invalid-email':
      return {
        code: errorCode,
        message: 'Please enter a valid email address.',
        field: 'email',
        severity: 'error'
      };
    
    case 'auth/user-disabled':
      return {
        code: errorCode,
        message: 'This account has been disabled. Please contact support for assistance.',
        field: 'general',
        severity: 'error'
      };
    
    case 'auth/too-many-requests':
      return {
        code: errorCode,
        message: 'Too many failed login attempts. Please try again later or reset your password.',
        field: 'general',
        severity: 'warning'
      };
    
    // Registration errors
    case 'auth/email-already-in-use':
      return {
        code: errorCode,
        message: 'An account with this email already exists. Please sign in instead.',
        field: 'email',
        severity: 'error'
      };
    
    case 'auth/weak-password':
      return {
        code: errorCode,
        message: 'Password is too weak. Please use at least 6 characters with a mix of letters and numbers.',
        field: 'password',
        severity: 'error'
      };
    
    case 'auth/operation-not-allowed':
      return {
        code: errorCode,
        message: 'Email/password accounts are not enabled. Please contact support.',
        field: 'general',
        severity: 'error'
      };
    
    // Network and general errors
    case 'auth/network-request-failed':
      return {
        code: errorCode,
        message: 'Network error. Please check your internet connection and try again.',
        field: 'general',
        severity: 'warning'
      };
    
    case 'auth/timeout':
      return {
        code: errorCode,
        message: 'Request timed out. Please try again.',
        field: 'general',
        severity: 'warning'
      };
    
    case 'auth/internal-error':
      return {
        code: errorCode,
        message: 'An internal error occurred. Please try again later.',
        field: 'general',
        severity: 'error'
      };
    
    // Google Sign-in errors
    case 'auth/popup-closed-by-user':
      return {
        code: errorCode,
        message: 'Sign-in was cancelled. Please try again.',
        field: 'general',
        severity: 'info'
      };
    
    case 'auth/popup-blocked':
      return {
        code: errorCode,
        message: 'Pop-up was blocked by your browser. Please allow pop-ups and try again.',
        field: 'general',
        severity: 'warning'
      };
    
    case 'auth/cancelled-popup-request':
      return {
        code: errorCode,
        message: 'Sign-in request was cancelled. Please try again.',
        field: 'general',
        severity: 'info'
      };
    
    // Default case
    default:
      return {
        code: errorCode,
        message: 'An unexpected error occurred. Please try again.',
        field: 'general',
        severity: 'error'
      };
  }
};

/**
 * Password validation utilities
 */
export interface PasswordValidation {
  isValid: boolean;
  errors: string[];
  strength: 'weak' | 'fair' | 'good' | 'strong';
}

export const validatePassword = (password: string): PasswordValidation => {
  const errors: string[] = [];
  let score = 0;
  
  // Length check
  if (password.length < 6) {
    errors.push('Password must be at least 6 characters long');
  } else if (password.length >= 8) {
    score += 1;
  }
  
  // Character variety checks
  if (!/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  } else {
    score += 1;
  }
  
  if (!/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  } else {
    score += 1;
  }
  
  if (!/\d/.test(password)) {
    errors.push('Password must contain at least one number');
  } else {
    score += 1;
  }
  
  if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
    errors.push('Password should contain at least one special character');
  } else {
    score += 1;
  }
  
  // Determine strength
  let strength: 'weak' | 'fair' | 'good' | 'strong' = 'weak';
  if (score >= 4) strength = 'strong';
  else if (score >= 3) strength = 'good';
  else if (score >= 2) strength = 'fair';
  
  return {
    isValid: errors.length === 0 && password.length >= 6,
    errors,
    strength
  };
};

/**
 * Email validation utility
 */
export const validateEmail = (email: string): { isValid: boolean; error?: string } => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  
  if (!email.trim()) {
    return { isValid: false, error: 'Email is required' };
  }
  
  if (!emailRegex.test(email)) {
    return { isValid: false, error: 'Please enter a valid email address' };
  }
  
  return { isValid: true };
};

/**
 * Full name validation utility
 */
export const validateFullName = (fullName: string): { isValid: boolean; error?: string } => {
  if (!fullName.trim()) {
    return { isValid: false, error: 'Full name is required' };
  }
  
  if (fullName.trim().length < 2) {
    return { isValid: false, error: 'Full name must be at least 2 characters long' };
  }
  
  if (!/^[a-zA-Z\s'-]+$/.test(fullName)) {
    return { isValid: false, error: 'Full name can only contain letters, spaces, hyphens, and apostrophes' };
  }
  
  return { isValid: true };
};
