import { Timestamp } from 'firebase/firestore';

export interface User {
  uid: string;
  email: string;
  fullName: string;
  role: 'user' | 'admin' | 'manager';
  location: {
    areaCode: string;
    city: string;
    state: string;
    country: string;
  };
  mobileNumber: string;
  profilePictureUrl?: string;
}

export interface Car {
  id: string;
  name: string;
  description: string;
  images: string[];
  pricing: {
    perHour: number;
    perDay: number;
    perWeek: number;
  };
}

export interface Booking {
  id: string;
  userId: string;
  carId: string;
  pickupDateTime: Timestamp;
  returnDateTime: Timestamp;
  totalPrice: number;
  status: 'pending' | 'verified' | 'picked-up' | 'dropped-off' | 'cancelled';
  documents: string[];
}
