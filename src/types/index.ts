import { Timestamp } from 'firebase/firestore';

export interface User {
  uid: string;
  email: string;
  fullName: string;
  role: 'user' | 'admin' | 'manager';
  location: {
    areaCode: string;
    city: string;
    state: string;
    country: string;
  };
  mobileNumber: string;
  profilePictureUrl?: string;
}

export interface Car {
  id: string;
  name: string;
  brand: string;
  model: string;
  year: number;
  description: string;
  images: string[];
  category: 'economy' | 'luxury' | 'suv' | 'sports' | 'sedan' | 'convertible';
  specifications: {
    transmission: 'automatic' | 'manual';
    fuelType: 'petrol' | 'diesel' | 'electric' | 'hybrid';
    seats: number;
    doors: number;
    engineSize: string;
    fuelConsumption: string;
  };
  features: string[];
  pricing: {
    perHour: number;
    perDay: number;
    perWeek: number;
  };
  availability: {
    isAvailable: boolean;
    location: string;
    pickupLocations: string[];
  };
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface Booking {
  id: string;
  userId: string;
  carId: string;
  pickupDateTime: Timestamp;
  returnDateTime: Timestamp;
  totalPrice: number;
  status: 'pending' | 'verified' | 'picked-up' | 'dropped-off' | 'cancelled';
  documents: string[];
}
