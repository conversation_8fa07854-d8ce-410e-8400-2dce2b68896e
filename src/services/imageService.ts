import { ref, uploadBytesResumable, getDownloadURL, deleteObject } from 'firebase/storage';
import { storage } from '../firebaseConfig';
import { UploadedImage } from '../components/ui/image-upload';

export interface ImageUploadResult {
  success: boolean;
  url?: string;
  error?: string;
}

export interface ImageUploadProgress {
  imageId: string;
  progress: number;
  status: 'uploading' | 'success' | 'error';
  url?: string;
  error?: string;
}

/**
 * Upload a single image to Firebase Storage
 */
export const uploadImage = async (
  file: File,
  path: string,
  onProgress?: (progress: number) => void
): Promise<ImageUploadResult> => {
  try {
    // Create a unique filename
    const timestamp = Date.now();
    const randomString = Math.random().toString(36).substring(2, 15);
    const fileExtension = file.name.split('.').pop();
    const fileName = `${timestamp}_${randomString}.${fileExtension}`;
    
    // Create storage reference
    const storageRef = ref(storage, `${path}/${fileName}`);
    
    // Create upload task
    const uploadTask = uploadBytesResumable(storageRef, file);
    
    return new Promise((resolve, reject) => {
      uploadTask.on(
        'state_changed',
        (snapshot) => {
          // Progress callback
          const progress = (snapshot.bytesTransferred / snapshot.totalBytes) * 100;
          if (onProgress) {
            onProgress(Math.round(progress));
          }
        },
        (error) => {
          // Error callback
          console.error('Upload error:', error);
          let errorMessage = 'Upload failed';
          
          switch (error.code) {
            case 'storage/unauthorized':
              errorMessage = 'Unauthorized to upload files';
              break;
            case 'storage/canceled':
              errorMessage = 'Upload was cancelled';
              break;
            case 'storage/quota-exceeded':
              errorMessage = 'Storage quota exceeded';
              break;
            case 'storage/invalid-format':
              errorMessage = 'Invalid file format';
              break;
            case 'storage/invalid-argument':
              errorMessage = 'Invalid file';
              break;
            default:
              errorMessage = error.message || 'Upload failed';
          }
          
          resolve({ success: false, error: errorMessage });
        },
        async () => {
          // Success callback
          try {
            const downloadURL = await getDownloadURL(uploadTask.snapshot.ref);
            resolve({ success: true, url: downloadURL });
          } catch (error) {
            resolve({ success: false, error: 'Failed to get download URL' });
          }
        }
      );
    });
  } catch (error) {
    console.error('Upload setup error:', error);
    return { success: false, error: 'Failed to start upload' };
  }
};

/**
 * Upload multiple images with progress tracking
 */
export const uploadMultipleImages = async (
  images: UploadedImage[],
  basePath: string,
  onProgress?: (updates: ImageUploadProgress[]) => void
): Promise<{ success: boolean; results: ImageUploadResult[]; urls: string[] }> => {
  const results: ImageUploadResult[] = [];
  const urls: string[] = [];
  const progressUpdates: ImageUploadProgress[] = [];
  
  // Initialize progress tracking
  images.forEach(image => {
    progressUpdates.push({
      imageId: image.id,
      progress: 0,
      status: 'uploading'
    });
  });
  
  try {
    // Upload all images concurrently
    const uploadPromises = images.map(async (image, index) => {
      if (image.uploadStatus === 'error') {
        // Skip images that already have errors
        results[index] = { success: false, error: image.errorMessage };
        return;
      }
      
      const result = await uploadImage(
        image.file,
        basePath,
        (progress) => {
          // Update progress for this specific image
          const updateIndex = progressUpdates.findIndex(p => p.imageId === image.id);
          if (updateIndex !== -1) {
            progressUpdates[updateIndex].progress = progress;
            if (onProgress) {
              onProgress([...progressUpdates]);
            }
          }
        }
      );
      
      results[index] = result;
      
      // Update final status
      const updateIndex = progressUpdates.findIndex(p => p.imageId === image.id);
      if (updateIndex !== -1) {
        if (result.success && result.url) {
          progressUpdates[updateIndex].status = 'success';
          progressUpdates[updateIndex].url = result.url;
          progressUpdates[updateIndex].progress = 100;
          urls[index] = result.url;
        } else {
          progressUpdates[updateIndex].status = 'error';
          progressUpdates[updateIndex].error = result.error;
        }
        
        if (onProgress) {
          onProgress([...progressUpdates]);
        }
      }
    });
    
    await Promise.all(uploadPromises);
    
    // Filter out failed uploads from URLs array
    const successfulUrls = urls.filter(url => url);
    const allSuccessful = results.every(result => result.success);
    
    return {
      success: allSuccessful,
      results,
      urls: successfulUrls
    };
    
  } catch (error) {
    console.error('Multiple upload error:', error);
    return {
      success: false,
      results: results.length > 0 ? results : [{ success: false, error: 'Upload failed' }],
      urls: []
    };
  }
};

/**
 * Delete an image from Firebase Storage
 */
export const deleteImage = async (imageUrl: string): Promise<{ success: boolean; error?: string }> => {
  try {
    // Extract the path from the URL
    const url = new URL(imageUrl);
    const pathMatch = url.pathname.match(/\/o\/(.+)\?/);
    
    if (!pathMatch) {
      return { success: false, error: 'Invalid image URL' };
    }
    
    const imagePath = decodeURIComponent(pathMatch[1]);
    const imageRef = ref(storage, imagePath);
    
    await deleteObject(imageRef);
    return { success: true };
    
  } catch (error: any) {
    console.error('Delete error:', error);
    
    let errorMessage = 'Failed to delete image';
    if (error.code === 'storage/object-not-found') {
      errorMessage = 'Image not found';
    } else if (error.code === 'storage/unauthorized') {
      errorMessage = 'Unauthorized to delete image';
    }
    
    return { success: false, error: errorMessage };
  }
};

/**
 * Delete multiple images
 */
export const deleteMultipleImages = async (
  imageUrls: string[]
): Promise<{ success: boolean; results: { url: string; success: boolean; error?: string }[] }> => {
  const results = await Promise.all(
    imageUrls.map(async (url) => {
      const result = await deleteImage(url);
      return {
        url,
        success: result.success,
        error: result.error
      };
    })
  );
  
  const allSuccessful = results.every(result => result.success);
  
  return {
    success: allSuccessful,
    results
  };
};

/**
 * Compress image before upload (optional utility)
 */
export const compressImage = (
  file: File,
  maxWidth: number = 1920,
  maxHeight: number = 1080,
  quality: number = 0.8
): Promise<File> => {
  return new Promise((resolve) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();
    
    img.onload = () => {
      // Calculate new dimensions
      let { width, height } = img;
      
      if (width > height) {
        if (width > maxWidth) {
          height = (height * maxWidth) / width;
          width = maxWidth;
        }
      } else {
        if (height > maxHeight) {
          width = (width * maxHeight) / height;
          height = maxHeight;
        }
      }
      
      canvas.width = width;
      canvas.height = height;
      
      // Draw and compress
      ctx?.drawImage(img, 0, 0, width, height);
      
      canvas.toBlob(
        (blob) => {
          if (blob) {
            const compressedFile = new File([blob], file.name, {
              type: file.type,
              lastModified: Date.now()
            });
            resolve(compressedFile);
          } else {
            resolve(file); // Return original if compression fails
          }
        },
        file.type,
        quality
      );
    };
    
    img.src = URL.createObjectURL(file);
  });
};
