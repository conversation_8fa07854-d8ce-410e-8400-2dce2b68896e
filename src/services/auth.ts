import { getAuth, <PERSON>Auth<PERSON>rovider, signInWithPopup, createUserWithEmailAndPassword, signInWithEmailAndPassword, signOut, sendPasswordResetEmail as firebaseSendPasswordResetEmail, sendEmailVerification } from 'firebase/auth';
import { doc, getDoc } from 'firebase/firestore';
import { app, db } from '../firebaseConfig';
import { createUserProfile } from './userService';
import { getAuthErrorMessage, AuthErrorInfo } from '../utils/authErrors';

const auth = getAuth(app);
const provider = new GoogleAuthProvider();

// Auth result interface
export interface AuthResult {
  success: boolean;
  user?: any;
  error?: AuthErrorInfo;
}

export const signInWithGoogle = async (): Promise<AuthResult> => {
  try {
    const result = await signInWithPopup(auth, provider);
    const user = result.user;

    // Check if the user already exists in Firestore
    const userRef = doc(db, 'users', user.uid);
    const userSnap = await getDoc(userRef);

    if (!userSnap.exists()) {
      // If the user doesn't exist, create a new profile
      await createUserProfile(user, { fullName: user.displayName });
    }

    return { success: true, user };
  } catch (error) {
    console.error('Error signing in with Google:', error);
    const authError = getAuthErrorMessage(error as AuthError);
    return { success: false, error: authError };
  }
};

export const registerWithEmailAndPassword = async (email: string, password: string): Promise<AuthResult> => {
  try {
    const userCredential = await createUserWithEmailAndPassword(auth, email, password);
    return { success: true, user: userCredential.user };
  } catch (error) {
    console.error('Error registering with email and password:', error);
    const authError = getAuthErrorMessage(error as AuthError);
    return { success: false, error: authError };
  }
};

export const loginWithEmailAndPassword = async (email: string, password: string): Promise<AuthResult> => {
  try {
    const userCredential = await signInWithEmailAndPassword(auth, email, password);
    return { success: true, user: userCredential.user };
  } catch (error) {
    console.error('Error logging in with email and password:', error);
    const authError = getAuthErrorMessage(error as AuthError);
    return { success: false, error: authError };
  }
};

export const signOutUser = async (): Promise<AuthResult> => {
  try {
    await signOut(auth);
    return { success: true };
  } catch (error) {
    console.error('Error signing out:', error);
    const authError = getAuthErrorMessage(error as AuthError);
    return { success: false, error: authError };
  }
};

export const sendPasswordResetEmail = async (email: string): Promise<AuthResult> => {
  try {
    await firebaseSendPasswordResetEmail(auth, email);
    return { success: true };
  } catch (error) {
    console.error('Error sending password reset email:', error);
    const authError = getAuthErrorMessage(error as AuthError);
    return { success: false, error: authError };
  }
};

export const sendVerificationEmail = async (): Promise<AuthResult> => {
  try {
    if (auth.currentUser) {
      await sendEmailVerification(auth.currentUser);
      return { success: true };
    }
    return { success: false, error: { code: 'auth/no-current-user', message: 'No user is currently signed in.', field: 'general', severity: 'error' } };
  } catch (error) {
    console.error('Error sending verification email:', error);
    const authError = getAuthErrorMessage(error as AuthError);
    return { success: false, error: authError };
  }
};