import { doc, setDoc } from 'firebase/firestore';
import { db } from '../firebaseConfig';
import { User } from '../types';

export const createUserProfile = async (userAuth: any, additionalData: any) => {
  if (!userAuth) return;

  const userRef = doc(db, 'users', userAuth.uid);

  const newUser: Partial<User> = {
    uid: userAuth.uid,
    email: userAuth.email,
    fullName: additionalData.fullName,
    role: 'user', // Default role
    // You can add default empty values for other fields here
    location: { areaCode: '', city: '', state: '', country: '' },
    mobileNumber: '',
  };

  try {
    await setDoc(userRef, newUser);
  } catch (error) {
    console.error("Error creating user profile:", error);
  }
};
