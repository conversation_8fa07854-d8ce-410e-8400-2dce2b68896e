import { collection, addDoc, serverTimestamp, doc, updateDoc, deleteDoc, getDocs, getDoc } from 'firebase/firestore';
import { db } from '../firebaseConfig';
import { Car } from '../types';
import { deleteMultipleImages } from './imageService';

export interface CarServiceResult {
  success: boolean;
  id?: string;
  error?: any;
  message?: string;
}

export const addCar = async (carData: Omit<Car, 'id' | 'createdAt' | 'updatedAt'>): Promise<CarServiceResult> => {
  try {
    const carWithTimestamps = {
      ...carData,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    };

    const docRef = await addDoc(collection(db, 'cars'), carWithTimestamps);
    return {
      success: true,
      id: docRef.id,
      message: 'Car added successfully'
    };
  } catch (error) {
    console.error("Error adding car:", error);
    return {
      success: false,
      error,
      message: 'Failed to add car'
    };
  }
};

export const updateCar = async (carId: string, carData: Partial<Omit<Car, 'id' | 'createdAt'>>): Promise<CarServiceResult> => {
  try {
    const carRef = doc(db, 'cars', carId);
    const updateData = {
      ...carData,
      updatedAt: serverTimestamp()
    };

    await updateDoc(carRef, updateData);
    return {
      success: true,
      id: carId,
      message: 'Car updated successfully'
    };
  } catch (error) {
    console.error("Error updating car:", error);
    return {
      success: false,
      error,
      message: 'Failed to update car'
    };
  }
};

export const deleteCar = async (carId: string): Promise<CarServiceResult> => {
  try {
    // First, get the car data to retrieve image URLs
    const carRef = doc(db, 'cars', carId);
    const carDoc = await getDoc(carRef);

    if (carDoc.exists()) {
      const carData = carDoc.data() as Car;

      // Delete associated images from storage
      if (carData.images && carData.images.length > 0) {
        await deleteMultipleImages(carData.images);
      }
    }

    // Delete the car document
    await deleteDoc(carRef);

    return {
      success: true,
      id: carId,
      message: 'Car deleted successfully'
    };
  } catch (error) {
    console.error("Error deleting car:", error);
    return {
      success: false,
      error,
      message: 'Failed to delete car'
    };
  }
};

export const getCar = async (carId: string): Promise<{ success: boolean; car?: Car; error?: any }> => {
  try {
    const carRef = doc(db, 'cars', carId);
    const carDoc = await getDoc(carRef);

    if (carDoc.exists()) {
      const carData = { id: carDoc.id, ...carDoc.data() } as Car;
      return { success: true, car: carData };
    } else {
      return { success: false, error: 'Car not found' };
    }
  } catch (error) {
    console.error("Error getting car:", error);
    return { success: false, error };
  }
};

export const getAllCars = async (): Promise<{ success: boolean; cars?: Car[]; error?: any }> => {
  try {
    const carsCollection = collection(db, 'cars');
    const carsSnapshot = await getDocs(carsCollection);

    const cars: Car[] = carsSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    } as Car));

    return { success: true, cars };
  } catch (error) {
    console.error("Error getting cars:", error);
    return { success: false, error };
  }
};
