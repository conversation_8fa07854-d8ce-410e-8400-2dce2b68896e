@tailwind base;
@tailwind components;
@tailwind utilities;

/* Car Rental Design System - Premium & Professional */

@layer base {
  :root {
    /* Core Brand Colors */
    --background: 0 0% 100%;
    --foreground: 220 13% 18%;
    
    /* Premium Gold Accent */
    --primary: 43 96% 56%;
    --primary-foreground: 0 0% 100%;
    --primary-hover: 43 96% 46%;
    
    /* Card & Surface Colors */
    --card: 0 0% 100%;
    --card-foreground: 220 13% 18%;
    --card-border: 220 13% 91%;
    
    /* Secondary Colors */
    --secondary: 220 14% 96%;
    --secondary-foreground: 220 13% 18%;
    
    /* Muted & Subtle */
    --muted: 220 14% 96%;
    --muted-foreground: 220 9% 46%;
    
    /* Accent Colors */
    --accent: 43 96% 56%;
    --accent-foreground: 0 0% 100%;
    
    /* Interactive States */
    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 43 96% 56%;
    
    /* Status Colors */
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;
    --success: 142 76% 36%;
    --success-foreground: 0 0% 100%;
    
    /* Popover */
    --popover: 0 0% 100%;
    --popover-foreground: 220 13% 18%;
    
    /* Design System */
    --radius: 0.75rem;
    
    /* Custom Gradients */
    --gradient-hero: linear-gradient(135deg, hsl(43 96% 56% / 0.1), hsl(43 96% 56% / 0.05));
    --gradient-card: linear-gradient(145deg, hsl(0 0% 100%), hsl(220 14% 98%));
    
    /* Shadows */
    --shadow-card: 0 4px 6px -1px hsl(220 13% 18% / 0.1), 0 2px 4px -1px hsl(220 13% 18% / 0.06);
    --shadow-hover: 0 10px 15px -3px hsl(220 13% 18% / 0.1), 0 4px 6px -2px hsl(220 13% 18% / 0.05);
    --shadow-button: 0 1px 2px 0 hsl(220 13% 18% / 0.05);
    
    /* Transitions */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-fast: all 0.15s ease;
    
    /* Shiny Gold Button Effects */
    --gold-gradient: linear-gradient(135deg, 
      hsl(43 96% 56%), 
      hsl(45 100% 62%), 
      hsl(43 96% 56%), 
      hsl(41 92% 50%)
    );
    --gold-shine: linear-gradient(135deg,
      hsl(45 100% 70% / 0.8),
      hsl(43 96% 56% / 0.9),
      hsl(41 92% 50% / 0.9),
      hsl(39 88% 44% / 0.8)
    );
    --gold-shadow: 0 4px 15px hsl(43 96% 56% / 0.4), 
                   0 2px 8px hsl(43 96% 56% / 0.3), 
                   inset 0 1px 0 hsl(45 100% 70% / 0.3);
    --gold-shadow-hover: 0 8px 25px hsl(43 96% 56% / 0.5), 
                         0 4px 12px hsl(43 96% 56% / 0.4), 
                         inset 0 1px 0 hsl(45 100% 70% / 0.4),
                         0 0 30px hsl(43 96% 56% / 0.3);

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    /* Core Brand Colors */
    --background: 220 13% 9%;
    --foreground: 0 0% 98%;
    
    /* Premium Gold Accent (same for dark) */
    --primary: 43 96% 56%;
    --primary-foreground: 220 13% 9%;
    --primary-hover: 43 96% 46%;
    
    /* Card & Surface Colors */
    --card: 220 13% 12%;
    --card-foreground: 0 0% 98%;
    --card-border: 220 13% 20%;
    
    /* Secondary Colors */
    --secondary: 220 13% 15%;
    --secondary-foreground: 0 0% 98%;
    
    /* Muted & Subtle */
    --muted: 220 13% 15%;
    --muted-foreground: 220 9% 70%;
    
    /* Accent Colors */
    --accent: 43 96% 56%;
    --accent-foreground: 220 13% 9%;
    
    /* Interactive States */
    --border: 220 13% 20%;
    --input: 220 13% 20%;
    --ring: 43 96% 56%;
    
    /* Status Colors */
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;
    --success: 142 76% 36%;
    --success-foreground: 0 0% 100%;
    
    /* Popover */
    --popover: 220 13% 12%;
    --popover-foreground: 0 0% 98%;
    
    /* Custom Gradients */
    --gradient-hero: linear-gradient(135deg, hsl(43 96% 56% / 0.15), hsl(43 96% 56% / 0.08));
    --gradient-card: linear-gradient(145deg, hsl(220 13% 12%), hsl(220 13% 10%));
    
    /* Shadows */
    --shadow-card: 0 4px 6px -1px hsl(0 0% 0% / 0.3), 0 2px 4px -1px hsl(0 0% 0% / 0.2);
    --shadow-hover: 0 10px 15px -3px hsl(0 0% 0% / 0.4), 0 4px 6px -2px hsl(0 0% 0% / 0.3);
    --shadow-button: 0 1px 2px 0 hsl(0 0% 0% / 0.2);
    
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans antialiased;
  }
}

@layer components {
  /* Button Variants */
  .btn-gradient {
    @apply bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary shadow-button;
    transition: var(--transition-fast);
  }
  
  .btn-ghost-gold {
    @apply border border-primary/20 text-primary hover:bg-primary/5 hover:border-primary/40;
    transition: var(--transition-fast);
  }
  
  /* Card Styles */
  .card-elevated {
    background: var(--gradient-card);
    box-shadow: var(--shadow-card);
    transition: var(--transition-smooth);
  }
  
  .card-elevated:hover {
    box-shadow: var(--shadow-hover);
    transform: translateY(-2px);
  }
  
  /* Hero Section */
  .hero-bg {
    background: var(--gradient-hero);
  }
  
  /* Star Rating */
  .star-rating {
    @apply flex items-center gap-1 text-primary;
  }
  
  /* Specification Icons */
  .spec-icon {
    @apply w-5 h-5 text-muted-foreground;
  }
  
  /* Animation Classes */
  .animate-slide-up {
    animation: slideUp 0.6s ease-out;
  }
  
  .animate-fade-in {
    animation: fadeIn 0.8s ease-out;
  }
  
  .animate-scale-in {
    animation: scaleIn 0.4s ease-out;
  }
}

@layer utilities {
  /* Custom Animations */
  @keyframes slideUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }
  
  @keyframes scaleIn {
    from {
      opacity: 0;
      transform: scale(0.9);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }
  
  /* Glass Effect */
  .glass-effect {
    @apply backdrop-blur-sm bg-white/10 border border-white/20;
  }
  
  /* Text Gradients */
  .text-gradient {
    @apply bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent;
  }
  
  /* Shimmer Animation */
  @keyframes shimmer {
    0% {
      transform: translateX(-100%);
    }
    100% {
      transform: translateX(100%);
    }
  }
  
  .btn-shiny-gold {
    position: relative;
    background: var(--gold-gradient);
    box-shadow: var(--gold-shadow);
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
  
  .btn-shiny-gold::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, 
      transparent, 
      hsl(45 100% 70% / 0.4), 
      transparent
    );
    transition: all 0.6s ease;
  }
  
  .btn-shiny-gold:hover {
    box-shadow: var(--gold-shadow-hover);
    transform: translateY(-1px) scale(1.02);
  }
  
  .btn-shiny-gold:hover::before {
    animation: shimmer 0.8s ease-in-out;
  }
  
  .btn-shiny-gold:active {
    transform: translateY(0) scale(1);
  }
}