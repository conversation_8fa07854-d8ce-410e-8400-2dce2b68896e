# Car Rental Project Requirements

## Application Flow

1.  **Authentication:**
    -   Users start by either **Registering** for a new account or **Logging in**.
    -   Upon successful login, the system identifies the user's **Access Level** (User, Manager, or Admin).

2.  **User Flow:**
    -   **Browse and Select:** Users can browse the available cars, select one, and choose their desired booking dates.
    -   **Document Upload:** Users upload documents for verification.
    -   **Admin Verification:** The user must wait for an administrator to verify the uploaded documents.
    -   **Payment:** Once verified, the user proceeds to the payment confirmation page, which includes the receipt and a pickup OTP.
    -   **Dashboard:** Users have access to a dashboard where they can edit their profile, manage saved cards, and view their booking history.

3.  **Admin Flow:**
    -   **Dashboard:** Admins have a dashboard to manage the platform.
    -   **Car Management:** Ad<PERSON> can view, add, and edit car listings.
    -   **User Management:** Admins are responsible for verifying user documents.
    -   **Booking Management:** <PERSON><PERSON> can track all bookings and update their status (e.g., Picked up, Dropped off).

4.  **Manager Flow:**
    -   This functionality is planned for future implementation.

5.  **Reminder System:**
    -   The system sends automated reminders for car pickups and drop-offs.

## Authentication Module

### Login Functionality
- Three access levels:
  - User
  - Manager
  - Admin
- Required fields:
  - Email
  - Password

### Registration Functionality
- Fields:
  - Profile Picture
  - Full Name
  - Email
  - Password
  - Location (Area Code, City, State, Country)
  - Mobile Number

## Car Booking Process

### 1. Car Selection
- User browses available cars.
- On selecting a car, user is redirected to the Booking Page.

### 2. Booking Functionality
- Select:
  - Pickup Date & Time
  - Return Date & Time
- Real-time price calculation based on duration.
- Choose Payment Method:
  - Credit/Debit Card
  - Stripe
  - PayPal
- Accept Terms & Conditions
- Proceed to Document Upload for verification.
- Upon admin verification, user proceeds to Payment Confirmation Page.

### 3. Payment Confirmation Page
- Displays:
  - Booking Details
  - Payment Receipt
  - Pickup Instructions
  - One-Time Password (OTP) for verification

## User Dashboard

- Edit Profile Picture, Name
- Option to add/manage Saved Cards
- View Booking History
- Upload/Update Documents

## Reminder Functionality

- Pickup Reminder: 2 hours before pickup time (via email/notification)
- Drop-off Reminder: 2 hours after scheduled drop-off time
- Drop-off Instruction Email sent after drop-off

## Admin Dashboard

### 1. Car Listing Screen
- View all listed vehicles
- Edit/Delete car entries

### 2. Car Add Screen
- Add a new car with:
  - Images
  - Description
  - Name
  - Pricing Details

### 3. Document Verification Screen
- View uploaded user documents
- Verify or Reject documents

### 4. Booking Tracking Screen
- Track all bookings
- Update booking status:
  - Picked Up
  - Dropped Off
