# Car Rental Project Tasks

## Phase 1: Core Functionality (High Priority)

- [ ] **1. Firestore Database Setup:**
  - [ ] Define data structures for `users`, `cars`, and `bookings`.

- [ ] **2. User Roles and Permissions:**
  - [ ] Implement role assignment (User, Admin) on registration.
  - [ ] Write Firestore Security Rules to secure the database.

- [ ] **3. Admin - Car Management:**
  - [ ] Build the admin page to add new cars.
  - [ ] Build the admin page to view, edit, and delete existing cars.

- [ ] **4. User - Car Browsing:**
  - [ ] Create a page to display all available cars.
  - [ ] Create a detailed view page for a single car.

## Phase 2: Booking and User Management (Medium Priority)

- [ ] **5. Initial Booking Flow:**
  - [ ] Implement the booking form (date selection, etc.).
  - [ ] Add document upload functionality.
  - [ ] Create bookings with a "pending verification" status.

- [ ] **6. Admin - Booking Verification:**
  - [ ] Create the admin page to view pending bookings and documents.
  - [ ] Implement the logic to approve or reject bookings.

- [ ] **7. User Dashboard:**
  - [ ] Build the user dashboard to view booking history.
  - [ ] Add functionality to edit user profile information.

## Phase 3: Payments and Notifications (Low Priority)

- [ ] **8. Payment Integration:**
  - [ ] Integrate a payment provider (e.g., Stripe).
  - [ ] Connect payment processing to the booking approval step.

- [ ] **9. Reminder System:**
  - [ ] Set up Firebase Cloud Functions for automated notifications.
  - [ ] Implement email/push notifications for pickups and drop-offs.

- [ ] **10. Manager Role & Advanced Features:**
  - [ ] Implement the "Manager" access level and its specific functionalities.
