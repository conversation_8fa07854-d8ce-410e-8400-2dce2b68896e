rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {

    // Users Collection
    match /users/{userId} {
      // A user can only read or update their own profile.
      allow read, update: if request.auth != null && request.auth.uid == userId;
      // A user can only be created if the user is authenticated.
      allow create: if request.auth != null;
      // Nobody can delete a user profile or list all users.
      allow delete, list: if false;
    }

    // Cars Collection
    match /cars/{carId} {
      // Any authenticated user can view the car listings.
      allow read: if request.auth != null;
      // Only an admin can create, update, or delete car listings.
      allow create, update, delete: if request.auth != null && get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }

    // Bookings Collection (to be secured later)
    match /bookings/{bookingId} {
      // Placeholder: allow read, write if request.auth != null;
      // We will refine this in a later task.
      allow read, write: if request.auth != null;
    }
  }
}
